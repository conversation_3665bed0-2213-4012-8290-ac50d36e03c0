<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include virtual="/shujuku.asp"-->
<!--#include file="../lib2/config.asp"-->
<!--#include file="../lib2/dingdingutil.asp"-->
<!--include file="../lib2/functions.asp"-->


<%
Dim jsonRequest, fso, logFile, logPath
data = Request.Form("data")
If data = "" Then
    data = Request.QueryString("data")
End If

' 将接收到的JSON数据保存到txt文件中
logPath = Server.MapPath("flow_data.txt")

Set fso = Server.CreateObject("Scripting.FileSystemObject")
Set logFile = fso.OpenTextFile(logPath, 8, True) ' 8表示追加写入,True表示如果文件不存在则创建

'data是根据$分割的
Dim arrData
arrData = Split(data, "$")

If UBound(arrData) < 8 Then
    Response.Write "{""code"":-1, ""msg"":""数据字段不完整""}"
    Response.End
End If

' 写入时间戳和JSON数据
logFile.WriteLine Now() & " - " & data
'logFile.Close

'遍历arrData数组,处理每个元素
Dim i, itemParts
For i = 0 To UBound(arrData)
    '每个item按/分割,只取分割后的第1个元素(索引为1),忽略第0个元素
    If InStr(arrData(i), "/") > 0 Then
        itemParts = Split(arrData(i), "/")
        If UBound(itemParts) >= 1 Then
            arrData(i) = itemParts(1) '只保留第1个元素
        End If
    End If
Next

//定义一个映射关系，名称->索引值，比如：公司主体->0
'定义字段名称与索引的映射关系
Dim FIELD_INDEX
Set FIELD_INDEX = CreateObject("Scripting.Dictionary")

'' 以下字段为自定义字段，索引值根据实际情况调整
FIELD_INDEX.Add "归属公司", 5
FIELD_INDEX.Add "申请部门", 6
FIELD_INDEX.Add "一级科目", 7
FIELD_INDEX.Add "项目", 8
FIELD_INDEX.Add "事由", 9
FIELD_INDEX.Add "金额", 10
FIELD_INDEX.Add "有无发票", 11
FIELD_INDEX.Add "支付账本", 12
FIELD_INDEX.Add "收款对象", 13

'使用映射关系获取数据
flow_id = arrData(0)
title = arrData(1)
create_time = arrData(2)
create_userid = arrData(3)
'李成
'create_userid = "*****************"
approval_result = arrData(4)

company_name = arrData(FIELD_INDEX("归属公司"))
dept_name = arrData(FIELD_INDEX("申请部门"))
subject = arrData(FIELD_INDEX("一级科目"))
project_name = arrData(FIELD_INDEX("项目"))
pay_comment = arrData(FIELD_INDEX("事由"))
amount = arrData(FIELD_INDEX("金额"))
is_invoice = arrData(FIELD_INDEX("有无发票"))
pay_account = arrData(FIELD_INDEX("支付账本"))
get_account = arrData(FIELD_INDEX("收款对象"))

'将有无发票字段转换为布尔值
Dim is_invoice_bool
If is_invoice = "是" Then
    is_invoice_bool = "True"
Else
    is_invoice_bool = "False"
End If

'response.write is_invoice
'response.end

'科目
subjectData = Split(subject, "|")
Dim first_subject, second_subject
first_subject = subject
second_subject = ""

'如果科目包含一级和二级，则分别提取
If UBound(subjectData) >= 1 Then
    first_subject = subjectData(0)
    second_subject = subjectData(1)
End If

'取消调试输出和终止
'response.write get_account
'response.end

'获取要更新的ID（从传入的数据中获取）
Dim update_id
update_id = flow_id  ' 假设flow_id就是要更新的ID，您可以根据实际情况调整

'先检查要更新的记录是否存在
Dim rsCheck, sql
sql = "SELECT COUNT(*) FROM [技术部测试管理表] WHERE ID=" & update_id
Set rsCheck = conn.Execute(sql)
If rsCheck(0) = 0 Then
    Response.Write "{""code"":-1,""msg"":""要更新的记录不存在""}"
    Response.End
End If
Set rsCheck = Nothing

'根据钉钉ID获取用户名称
sql3="select mingzhi from user2011 where dingding_id='"&create_userid&"'"
rs.open sql3,conn,1,2
if not rs.eof then
    mingcheng = rs(0)
else
    mingcheng = ""
end if
rs.close

'根据审批结果确定上架/下架操作
Dim update_sql, operation_type
If approval_result = "同意" Or approval_result = "通过" Then
    '审批通过，执行上架操作
    operation_type = "上架"
    update_sql = "UPDATE [技术部测试管理表] SET " & _
                 "[上架日期] = '" & Now() & "', " & _
                 "[上架人] = '" & mingcheng & "' " & _
                 "WHERE ID = " & update_id
Else
    '审批拒绝或其他状态，执行下架操作
    operation_type = "下架"
    update_sql = "UPDATE [技术部测试管理表] SET " & _
                 "[下架日期] = '" & Now() & "', " & _
                 "[下架人] = '" & mingcheng & "' " & _
                 "WHERE ID = " & update_id
End If

'执行更新操作
On Error Resume Next
conn.Execute update_sql
If Err.Number <> 0 Then
    Response.Write "{""code"":-1,""msg"":""更新失败: " & Err.Description & """}"
    Response.End
End If
On Error GoTo 0

'记录日志
logFile.WriteLine operation_type & "操作成功 - ID: " & update_id & " - 操作人: " & mingcheng & " - SQL: " & update_sql
logFile.Close

'关闭记录集
rs2.Close
Set rs2 = Nothing

Set logFile = Nothing
Set fso = Nothing

' 返回成功响应
Response.Write "{""code"":1}"
%>