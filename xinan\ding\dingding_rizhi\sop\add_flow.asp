<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include virtual="/shujuku.asp"-->
<!--#include file="../lib2/config.asp"-->
<!--#include file="../lib2/dingdingutil.asp"-->
<!--include file="../lib2/functions.asp"-->


<%
Dim jsonRequest, fso, logFile, logPath
data = Request.Form("data")
If data = "" Then
    data = Request.QueryString("data")
End If

' 将接收到的JSON数据保存到txt文件中
logPath = Server.MapPath("flow_data.txt")

Set fso = Server.CreateObject("Scripting.FileSystemObject")
Set logFile = fso.OpenTextFile(logPath, 8, True) ' 8表示追加写入,True表示如果文件不存在则创建

'data是根据$分割的
Dim arrData
arrData = Split(data, "$")

If UBound(arrData) < 8 Then
    Response.Write "{""code"":-1, ""msg"":""数据字段不完整""}"
    Response.End
End If

' 写入时间戳和JSON数据
logFile.WriteLine Now() & " - " & data
'logFile.Close

'遍历arrData数组,处理每个元素
Dim i, itemParts
For i = 0 To UBound(arrData)
    '每个item按/分割,只取分割后的第1个元素(索引为1),忽略第0个元素
    If InStr(arrData(i), "/") > 0 Then
        itemParts = Split(arrData(i), "/")
        If UBound(itemParts) >= 1 Then
            arrData(i) = itemParts(1) '只保留第1个元素
        End If
    End If
Next

'获取基本数据
flow_id = arrData(0)  ' 这里的flow_id将作为要更新记录的ID
title = arrData(1)
create_time = arrData(2)
create_userid = arrData(3)
approval_result = arrData(4)

'获取要更新的ID（从传入的数据中获取）
Dim update_id
update_id = flow_id  ' 假设flow_id就是要更新的ID，您可以根据实际情况调整

'先检查要更新的记录是否存在
Dim rsCheck, sql
sql = "SELECT COUNT(*) FROM [技术部测试管理表] WHERE ID=" & update_id
Set rsCheck = conn.Execute(sql)
If rsCheck(0) = 0 Then
    Response.Write "{""code"":-1,""msg"":""要更新的记录不存在""}"
    Response.End
End If
Set rsCheck = Nothing

'根据钉钉ID获取用户名称
sql3="select mingzhi from user2011 where dingding_id='"&create_userid&"'"
rs.open sql3,conn,1,2
if not rs.eof then
    mingcheng = rs(0)
else
    mingcheng = ""
end if
rs.close

'根据审批结果确定上架/下架操作
Dim update_sql, operation_type
If approval_result = "同意" Or approval_result = "通过" Then
    '审批通过，执行上架操作
    operation_type = "上架"
    update_sql = "UPDATE [技术部测试管理表] SET " & _
                 "[上架日期] = '" & Now() & "', " & _
                 "[上架人] = '" & mingcheng & "' " & _
                 "WHERE ID = " & update_id
Else
    '审批拒绝或其他状态，执行下架操作
    operation_type = "下架"
    update_sql = "UPDATE [技术部测试管理表] SET " & _
                 "[下架日期] = '" & Now() & "', " & _
                 "[下架人] = '" & mingcheng & "' " & _
                 "WHERE ID = " & update_id
End If

'执行更新操作
On Error Resume Next
conn.Execute update_sql
If Err.Number <> 0 Then
    Response.Write "{""code"":-1,""msg"":""更新失败: " & Err.Description & """}"
    Response.End
End If
On Error GoTo 0

'记录日志
logFile.WriteLine operation_type & "操作成功 - ID: " & update_id & " - 操作人: " & mingcheng & " - SQL: " & update_sql
logFile.Close

Set logFile = Nothing
Set fso = Nothing

' 返回成功响应
Response.Write "{""code"":1,""msg"":""" & operation_type & "操作成功""}"
%>