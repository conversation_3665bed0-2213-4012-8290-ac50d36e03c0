<%@LANGUAGE="VBSCRIPT" CODEPAGE="65001"%>
<!--#include virtual="/shujuku.asp"-->
<!--#include file="../lib2/config.asp"-->
<!--#include file="../lib2/dingdingutil.asp"-->
<!--include file="../lib2/functions.asp"-->


<%
Dim jsonRequest, fso, logFile, logPath
data = Request.Form("data")
If data = "" Then
    data = Request.QueryString("data")
End If

' 将接收到的JSON数据保存到txt文件中
logPath = Server.MapPath("flow_data.txt")

Set fso = Server.CreateObject("Scripting.FileSystemObject")
Set logFile = fso.OpenTextFile(logPath, 8, True) ' 8表示追加写入,True表示如果文件不存在则创建

'data是根据$分割的
Dim arrData
arrData = Split(data, "$")

If UBound(arrData) < 8 Then
    Response.Write "{""code"":-1, ""msg"":""数据字段不完整""}"
    Response.End
End If

' 写入时间戳和JSON数据
logFile.WriteLine Now() & " - " & data
'logFile.Close

'遍历arrData数组,处理每个元素
Dim i, itemParts
For i = 0 To UBound(arrData)
    '每个item按/分割,只取分割后的第1个元素(索引为1),忽略第0个元素
    If InStr(arrData(i), "/") > 0 Then
        itemParts = Split(arrData(i), "/")
        If UBound(itemParts) >= 1 Then
            arrData(i) = itemParts(1) '只保留第1个元素
        End If
    End If
Next

'获取基本数据
flow_id = arrData(0)  ' 这里的flow_id将作为要更新记录的ID
title = arrData(1)
create_time = arrData(2)
create_userid = arrData(3)
approval_result = arrData(4)

'获取前端传递的4个字段值
shangjia_date = arrData(5)    ' 上架日期
xiajia_date = arrData(6)      ' 下架日期
shangjia_person = arrData(7)  ' 上架人
xiajia_person = arrData(8)    ' 下架人

'获取要更新的ID（从传入的数据中获取）
Dim update_id
update_id = flow_id  ' 假设flow_id就是要更新的ID，您可以根据实际情况调整

'先检查要更新的记录是否存在
Dim rsCheck, sql
sql = "SELECT COUNT(*) FROM [技术部测试管理表] WHERE ID=" & update_id
Set rsCheck = conn.Execute(sql)
If rsCheck(0) = 0 Then
    Response.Write "{""code"":-1,""msg"":""要更新的记录不存在""}"
    Response.End
End If
Set rsCheck = Nothing

'构建更新SQL，使用前端传递的值
Dim update_sql
Dim update_fields()
Dim field_count
field_count = 0

'动态构建更新字段
If shangjia_date <> "" Then
    ReDim Preserve update_fields(field_count)
    update_fields(field_count) = "[上架日期] = '" & shangjia_date & "'"
    field_count = field_count + 1
End If

If xiajia_date <> "" Then
    ReDim Preserve update_fields(field_count)
    update_fields(field_count) = "[下架日期] = '" & xiajia_date & "'"
    field_count = field_count + 1
End If

If shangjia_person <> "" Then
    ReDim Preserve update_fields(field_count)
    update_fields(field_count) = "[上架人] = '" & shangjia_person & "'"
    field_count = field_count + 1
End If

If xiajia_person <> "" Then
    ReDim Preserve update_fields(field_count)
    update_fields(field_count) = "[下架人] = '" & xiajia_person & "'"
    field_count = field_count + 1
End If

'检查是否有字段需要更新
If field_count = 0 Then
    Response.Write "{""code"":-1,""msg"":""没有需要更新的字段""}"
    Response.End
End If

'构建完整的更新SQL
update_sql = "UPDATE [技术部测试管理表] SET " & Join(update_fields, ", ") & " WHERE ID = " & update_id

'执行更新操作
On Error Resume Next
conn.Execute update_sql
If Err.Number <> 0 Then
    Response.Write "{""code"":-1,""msg"":""更新失败: " & Err.Description & """}"
    Response.End
End If
On Error GoTo 0

'记录日志
logFile.WriteLine operation_type & "操作成功 - ID: " & update_id & " - 操作人: " & mingcheng & " - SQL: " & update_sql
logFile.Close

Set logFile = Nothing
Set fso = Nothing

' 返回成功响应
Response.Write "{""code"":1,""msg"":""" & operation_type & "操作成功""}"
%>